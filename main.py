import requests, json, os, pandas as pd
from auth import auth
from datetime import datetime


access_token = auth(os.getenv("CLIENT_ID"), os.getenv("CLIENT_SECRET")).get_token()

run_time = datetime.now().strftime("%Y-%m-%d_%H%M%S")

headers = {
    "Accept": "application/json",
    "Authorization": f"Bearer {access_token}"
}



def get_people() -> pd.DataFrame:
    page = 1
    data = []
    api_response = {}

    while api_response.get("error") is None:
        api_url = f"https://avalon.infoodle.com/apiv2/people/search/all/*/{page}"

        api_response = requests.get(api_url, headers=headers).json()
        data.extend(api_response.get("people", []))
        page += 1

    df = pd.json_normalize(data)
    #df.to_csv(f'people_{run_time}.csv')

    return df


def get_notes(client_ids):
    data = []

    def get_page(unique_id, page):
        api_url = f"https://avalon.infoodle.com/apiv2/notes/list/{unique_id}"

        api_response = requests.get(api_url, headers=headers).json()

        if api_response.get("items"):

            # add unique_id foreign key
            for item in api_response["items"]:
                item["unique_id"] = unique_id

            data.extend(api_response.get("items", []))

        if page < api_response["paging"]["pages"]:
            return page + 1
        
        return None
    

    for unique_id in client_ids:
        page = 1

        next_page = get_page(unique_id, page)
        page = next_page

        while next_page:
            next_page = get_page(unique_id, page)
            page = next_page
        

    df = pd.json_normalize(data)
    df.to_csv(f'notes_{run_time}.csv')

    return df

def get_form():
    api_url = f"https://avalon.infoodle.com/apiv2/form/list"

    api_response = requests.get(api_url, headers=headers).json()

    df = pd.json_normalize(api_response)
    df.to_csv(f'forms_{run_time}.csv')

    return df

def get_form_entries(fromthisid):
    data = []
    api_url = f"https://avalon.infoodle.com/apiv2/formentries/list/{fromthisid}"

    api_response = requests.get(api_url, headers=headers).json()
    data.extend(api_response)

    while len(api_response) == 100:
        fromthisid = api_response[-1].get("id")
        if fromthisid is None:
            break

        api_url = f"https://avalon.infoodle.com/apiv2/formentries/list/{fromthisid}"
        api_response = requests.get(api_url, headers=headers).json()
        data.extend(api_response)

    df = pd.DataFrame(data)
    df.to_csv(f'form_entries_{run_time}.csv')

    return df

# Need to handle rate limits and errors
def get_form_entry_fields(id):

    try:
        api_url = f"https://avalon.infoodle.com/apiv2/formentryfields/{id}"
        api_response = requests.get(api_url, headers=headers)
        
        #print(api_response.status_code)
        #print(api_response.json())

        rsp = api_response.json()

        for d in rsp:
            d["id"] = id

        return rsp
    
    except:
        print(id)

        return None
    



#people_df = get_people()

#client_ids = (
#    people_df.loc[people_df["person_custom_fields.PERSONAL.Stakeholder Type .value"] == "Client", "unique_id"]
#    .unique()
#    .tolist()
#)

# For testing
#client_ids = ['1111652', '481652', '359127', '316408', '1341943', '1528157', '399709', '1474953', '1545632', '1523983', '1540000', '391361', '1542137', '1109515', '1471458', '716117', '1170488', '1239709', '1444177', '382234', '1150876', '1475632', '485147', '1567381', '362622', '419321', '1042331', '326214', '709806', '1392331', '1443498', '1229903', '1541458', '469709', '1110294', '1058448', '1507866', '1092719', '404662', '403204', '1540779', '1537284', '1430876', '378739', '378060', '1192137', '504759', '1502913', '1568060', '1506408', '1093498', '1391652', '1407769', '1533010', '324856', '712622', '1518351', '1468642', '1543595', '1255147', '1213886', '1522525', '1432913', '1532331', '1486896', '498448', '1344759', '468351', '1416117', '1526799', '1512719', '1509224', '1548448', '1267090', '753983', '1508545', '1527478', '339515', '459224', '1488254', '1537963', '1472137', '1187963', '1458836', '1516214', '1166993']
#get_notes(client_ids)

#df = get_form()




#df = get_form_entries(0)
#form_entry_ids = df["id"].tolist()

#For Testing
#form_entry_ids = [5781, 5782, 5783, 5784, 5785, 5786, 5787, 5788, 5789, 5790, 5791, 5792, 5793, 5794, 5795, 5796, 5797, 5799, 5800, 5801, 5802, 5803, 5807, 5808, 5809, 5810, 5811, 5812, 5813, 5814, 5815]
#data = []

#for id in form_entry_ids:
#    form_data = get_form_entry_fields(id)
#    data.extend(form_data)

#df = pd.DataFrame(data)
#df.to_csv(f'form_entry_field_data_{run_time}.csv')


