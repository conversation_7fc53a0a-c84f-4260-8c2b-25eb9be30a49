import requests, logging
from urllib.parse import urlparse, parse_qs

# ===== CONFIG =====
REDIRECT_URI = f"https://avalon.infoodle.com/apiv2_auth"

class auth():
    token = None
    def __init__(self, client_id, client_secret):
        self.client_id = client_id
        self.client_secret = client_secret
        
    def get_token(self, force=False):
        if self.token and not(force):
            return self.token

        # ===== STEP 1: POST authorisation back =====
        step1_url = f"https://avalon.infoodle.com/apiv2/oauth2/authorise"
        step1_data = {
            "client_id": self.client_id,
            "redirect_uri": REDIRECT_URI,
            "response_type": "code",
            "authorized": "yes"
        }
        r2 = requests.post(step1_url, data=step1_data, allow_redirects=False)

        # Extract the code from Location header (redirect URL)
        location_header = r2.headers.get("Location", "")
        parsed = urlparse(location_header)
        code = parse_qs(parsed.query).get("code", [None])[0]

        # ===== STEP 2: Exchange code for token =====
        step2_url = f"https://avalon.infoodle.com/apiv2/oauth2/token"
        step2_data = {
            "grant_type": "authorization_code",
            "code": code,
            "client_secret": self.client_secret,
            "client_id": self.client_id,
            "redirect_uri": REDIRECT_URI
        }
        r3 = requests.post(step2_url, data=step2_data)
        logging.info(f"Auth Status: {r3.status_code}")

        self.token = r3.json().get("access_token")
        return self.token